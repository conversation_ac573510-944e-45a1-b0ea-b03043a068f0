# TripXplo Family EMI Website - Deployment Guide

## 🚀 Quick Deployment for family.tripxplo.com

This guide will help you deploy the Family EMI website to family.tripxplo.com with clean URLs (no index.html needed).

---

## 📋 Prerequisites

- Domain: family.tripxplo.com pointing to your server
- Web server: Apache or Nginx
- SSL certificate for HTTPS
- Database access to CRM and Quote databases

---

## 🔧 Deployment Steps

### 1. Upload Files

Upload all files from the `src/nest/` folder to your web server:

```bash
# Example for Apache
sudo cp -r src/nest/* /var/www/family-emi/

# Set proper permissions
sudo chown -R www-data:www-data /var/www/family-emi/
sudo chmod -R 755 /var/www/family-emi/
```

### 2. Configure Web Server

#### For Apache:
- The `.htaccess` file is already included
- Ensure mod_rewrite is enabled: `sudo a2enmod rewrite`
- Restart Apache: `sudo systemctl restart apache2`

#### For Nginx:
- Copy `nginx.conf` to `/etc/nginx/sites-available/family-emi`
- Create symlink: `sudo ln -s /etc/nginx/sites-available/family-emi /etc/nginx/sites-enabled/`
- Update SSL certificate paths in the config
- Test config: `sudo nginx -t`
- Restart Nginx: `sudo systemctl restart nginx`

### 3. Update Database Configuration

Edit `js/config.js` with your actual database credentials:

```javascript
const CONFIG = {
  // CRM Database (Family Types)
  CRM_DB_URL: 'https://tlfwcnikdlwoliqzavxj.supabase.co',
  CRM_ANON_KEY: 'your_actual_crm_anon_key_here',
  
  // Quote Database (Packages & EMI)
  QUOTE_DB_URL: 'https://lkqbrlrmrsnbtkoryazq.supabase.co',
  QUOTE_ANON_KEY: 'your_actual_quote_anon_key_here'
};
```

### 4. Test the Deployment

1. Visit https://family.tripxplo.com
2. Verify the logo loads correctly
3. Test destination search
4. Test date picker functionality
5. Test family type detection
6. Test package search

---

## ✅ Features Fixed

### 1. ✅ Ferry Cost Calculation
- Fixed to multiply by total persons excluding infants
- Formula: `ferry_cost * (adults + children + child)`

### 2. ✅ Logo in Header
- Updated to use: `https://tripemilestone.in-maa-1.linodeobjects.com/logo%2Ftripxplo-logo.png`
- Added proper CSS styling

### 3. ✅ Enhanced Package Details
- Fetches real data from Quote Generator DB
- Shows actual inclusions, exclusions, hotel details
- Displays meal plans from hotel data
- Shows number of nights and hotels

### 4. ✅ Improved Search UX
- Better date picker with quick selection buttons
- Enhanced destination autocomplete
- Form validation with user feedback
- Loading states for better UX

### 5. ✅ Clean URLs
- No need for index.html in URLs
- SPA-style routing configured
- Works with both Apache and Nginx

---

## 🔍 Troubleshooting

### Logo Not Loading
- Check if the logo URL is accessible
- Verify CORS settings if needed

### Database Connection Issues
- Verify Supabase URLs and keys in config.js
- Check browser console for errors
- Ensure RLS policies allow public access

### Search Not Working
- Check browser console for JavaScript errors
- Verify database service is running
- Test API endpoints manually

### Clean URLs Not Working
- For Apache: Ensure mod_rewrite is enabled
- For Nginx: Check nginx config syntax
- Verify file permissions

---

## 📞 Support

If you encounter issues:
1. Check browser console for errors
2. Verify server logs
3. Test database connectivity
4. Contact technical support

---

## 🎯 Next Steps

After deployment:
1. Set up monitoring for uptime
2. Configure backup for database
3. Set up analytics tracking
4. Test EMI functionality end-to-end
5. Train team on new features
