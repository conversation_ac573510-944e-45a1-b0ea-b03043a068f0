/**
 * Direct Database Service for Family EMI
 * Connects directly to Supabase databases for live data
 */

class DatabaseService {
  constructor() {
    // Initialize Supabase clients
    this.initializeClients();
    this.sessionId = this.generateSessionId();
    
    console.log('🗄️ Database Service initialized');
  }
  
  initializeClients() {
    // Check if CONFIG is available
    if (typeof CONFIG === 'undefined') {
      console.error('❌ CONFIG not loaded. Please include config.js before databaseService.js');
      return;
    }

    // Validate configuration
    if (CONFIG.CRM_ANON_KEY === 'YOUR_CRM_DATABASE_ANON_KEY_HERE' ||
        CONFIG.QUOTE_ANON_KEY === 'YOUR_QUOTE_DATABASE_ANON_KEY_HERE') {
      console.warn('⚠️ Please update the database keys in js/config.js');
    }

    // CRM Database (Family Types)
    this.crmDB = supabase.createClient(
      CONFIG.CRM_DB_URL,
      CONFIG.CRM_ANON_KEY
    );

    // Quote Database (Packages & EMI)
    this.quoteDB = supabase.createClient(
      CONFIG.QUOTE_DB_URL,
      CONFIG.QUOTE_ANON_KEY
    );

    console.log('🔗 Database clients initialized');
  }
  
  generateSessionId() {
    return `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  // Get all family types from CRM database
  async getFamilyTypes() {
    try {
      console.log('📊 Fetching family types from CRM database...');
      
      const { data, error } = await this.crmDB
        .from('family_type')
        .select('*')
        .order('family_type');
      
      if (error) {
        console.error('❌ Error fetching family types:', error);
        throw error;
      }
      
      console.log('✅ Loaded family types:', data.length);
      
      // Format data for frontend
      const formattedData = data.map(ft => ({
        ...ft,
        composition: this.formatFamilyComposition(ft)
      }));
      
      return { success: true, data: formattedData };
    } catch (error) {
      console.error('Database error in getFamilyTypes:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Get destinations from quote database - using actual Quote Generator structure
  async getDestinations() {
    try {
      console.log('🗺️ Fetching destinations from Quote Generator database...');

      // Method 1: Try to get destinations from family_type_prices table (if it has data)
      const { data: pricesData, error: pricesError } = await this.quoteDB
        .from('family_type_prices')
        .select('destination, destination_category')
        .eq('is_public_visible', true)
        .not('destination', 'is', null)
        .limit(50);

      if (!pricesError && pricesData && pricesData.length > 0) {
        console.log('📊 Found destinations in family_type_prices:', pricesData.length);

        // Get unique destinations with categories
        const destinationMap = new Map();
        pricesData.forEach(item => {
          if (item.destination && item.destination.trim()) {
            destinationMap.set(item.destination, {
              destination: item.destination,
              category: item.destination_category || 'General',
              packages_available: (destinationMap.get(item.destination)?.packages_available || 0) + 1
            });
          }
        });

        const destinations = Array.from(destinationMap.values())
          .sort((a, b) => a.destination.localeCompare(b.destination));

        console.log('✅ Loaded destinations from family_type_prices:', destinations.length);
        return { success: true, data: destinations };
      }

      // Method 2: Try to get destinations from quotes table via quote_mappings
      console.log('🔍 Trying quote_mappings table...');
      const { data: mappingsData, error: mappingsError } = await this.quoteDB
        .from('quote_mappings')
        .select('destination')
        .not('destination', 'is', null)
        .limit(50);

      if (!mappingsError && mappingsData && mappingsData.length > 0) {
        console.log('📊 Found destinations in quote_mappings:', mappingsData.length);

        const destinations = [...new Set(mappingsData.map(item => item.destination))]
          .filter(dest => dest && dest.trim() !== '')
          .map(dest => ({
            destination: dest,
            category: 'Available',
            packages_available: mappingsData.filter(item => item.destination === dest).length
          }))
          .sort((a, b) => a.destination.localeCompare(b.destination));

        console.log('✅ Loaded destinations from quote_mappings:', destinations.length);
        return { success: true, data: destinations };
      }

      // Method 3: Try direct quotes table
      console.log('🔍 Trying quotes table...');
      const { data: quotesData, error: quotesError } = await this.quoteDB
        .from('quotes')
        .select('destination')
        .not('destination', 'is', null)
        .limit(50);

      if (!quotesError && quotesData && quotesData.length > 0) {
        console.log('📊 Found destinations in quotes table:', quotesData.length);

        const destinations = [...new Set(quotesData.map(item => item.destination))]
          .filter(dest => dest && dest.trim() !== '')
          .map(dest => ({
            destination: dest,
            category: 'Available',
            packages_available: quotesData.filter(item => item.destination === dest).length
          }))
          .sort((a, b) => a.destination.localeCompare(b.destination));

        console.log('✅ Loaded destinations from quotes table:', destinations.length);
        return { success: true, data: destinations };
      }

      // Fallback: Return popular Indian destinations
      console.warn('⚠️ No destinations found in database, using popular destinations');
      return {
        success: true,
        data: [
          { destination: 'Kashmir', category: 'Hill Station', packages_available: 0 },
          { destination: 'Goa', category: 'Beach', packages_available: 0 },
          { destination: 'Manali', category: 'Hill Station', packages_available: 0 },
          { destination: 'Kerala', category: 'Backwaters', packages_available: 0 },
          { destination: 'Rajasthan', category: 'Desert', packages_available: 0 },
          { destination: 'Himachal Pradesh', category: 'Hill Station', packages_available: 0 },
          { destination: 'Uttarakhand', category: 'Hill Station', packages_available: 0 },
          { destination: 'Andaman', category: 'Island', packages_available: 0 }
        ]
      };

    } catch (error) {
      console.error('Database error in getDestinations:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Search packages based on criteria
  async searchPackages(searchParams) {
    try {
      console.log('🔍 Searching packages with params:', searchParams);
      
      const { destination, adults, children, infants } = searchParams;
      
      // Step 1: Detect family type
      const familyType = await this.detectFamilyType(adults, children || 0, infants || 0);
      console.log('🎯 Detected family type:', familyType);

      // Step 2: Search for packages using Quote Generator database structure
      console.log('🔍 Searching packages in Quote Generator database...');
      console.log('🎯 Family Type:', familyType);
      console.log('🗺️ Destination:', destination);

      // Method 1: Try family_type_prices table with family type filtering
      let query = this.quoteDB
        .from('family_type_prices')
        .select('*')
        .eq('is_public_visible', true);

      // Filter by destination
      if (destination) {
        query = query.ilike('destination', `%${destination}%`);
      }

      // Filter by family type (exact match first, then compatible)
      if (familyType && familyType.family_id !== 'CUSTOM') {
        query = query.eq('family_type_id', familyType.family_id);
      }

      // Order by display order and limit results
      query = query.order('public_display_order', { ascending: true })
                   .order('created_at', { ascending: false })
                   .limit(20);

      console.log('🔍 Executing family_type_prices query...');
      const { data: packages, error } = await query;

      if (error) {
        console.error('❌ Error in family_type_prices query:', error);
        console.log('🔄 Trying alternative approach...');

        // Method 2: Try to get packages from quote_mappings + quotes
        const { data: mappingsData, error: mappingsError } = await this.quoteDB
          .from('quote_mappings')
          .select(`
            *,
            quotes!inner(*)
          `)
          .ilike('destination', `%${destination}%`)
          .limit(10);

        if (mappingsError) {
          console.error('❌ Error in quote_mappings query:', mappingsError);
          // Return sample packages as fallback
          return this.createSamplePackages(destination, familyType, searchParams);
        }

        if (mappingsData && mappingsData.length > 0) {
          console.log('✅ Found packages via quote_mappings:', mappingsData.length);

          // Convert quote mappings to package format
          const convertedPackages = mappingsData.map(mapping =>
            this.convertQuoteMappingToPackage(mapping, familyType)
          );

          return {
            success: true,
            matched_family_type: familyType,
            packages: convertedPackages,
            search_params: searchParams
          };
        }

        // Method 3: Direct quotes table search
        const { data: quotesData, error: quotesError } = await this.quoteDB
          .from('quotes')
          .select('*')
          .ilike('destination', `%${destination}%`)
          .limit(10);

        if (!quotesError && quotesData && quotesData.length > 0) {
          console.log('✅ Found packages via quotes table:', quotesData.length);

          const convertedPackages = quotesData.map(quote =>
            this.convertQuoteToPackage(quote, familyType)
          );

          return {
            success: true,
            matched_family_type: familyType,
            packages: convertedPackages,
            search_params: searchParams
          };
        }

        // Final fallback
        console.warn('⚠️ No packages found in any table, creating sample packages');
        return this.createSamplePackages(destination, familyType, searchParams);
      }

      console.log('📊 Raw packages from family_type_prices:', packages?.length || 0);

      if (!packages || packages.length === 0) {
        console.log('🔄 No exact family type matches, trying broader search...');

        // Try without family type filter
        let broadQuery = this.quoteDB
          .from('family_type_prices')
          .select('*')
          .eq('is_public_visible', true);

        if (destination) {
          broadQuery = broadQuery.ilike('destination', `%${destination}%`);
        }

        broadQuery = broadQuery.order('public_display_order', { ascending: true })
                              .limit(10);

        const { data: broadPackages, error: broadError } = await broadQuery;

        if (!broadError && broadPackages && broadPackages.length > 0) {
          console.log('✅ Found packages with broader search:', broadPackages.length);
          packages = broadPackages;
        } else {
          console.warn('⚠️ No packages found even with broad search, creating sample packages');
          return this.createSamplePackages(destination, familyType, searchParams);
        }
      }

      if (error) {
        console.error('❌ Error searching packages:', error);
        throw error;
      }

      // Step 3: Get EMI plans separately if packages found
      let packagesWithEMI = packages;
      if (packages && packages.length > 0) {
        try {
          // Try to get EMI plans separately
          const { data: emiPlans, error: emiError } = await this.quoteDB
            .from('family_type_emi_plans')
            .select('*');

          if (!emiError && emiPlans) {
            // Attach EMI plans to packages (you can customize this logic)
            packagesWithEMI = packages.map(pkg => ({
              ...pkg,
              family_type_emi_plans: emiPlans.slice(0, 3) // Take first 3 EMI plans as sample
            }));
          }
        } catch (emiError) {
          console.warn('⚠️ Could not load EMI plans, using packages without EMI:', emiError);
        }
      }
      
      console.log('✅ Found packages:', packagesWithEMI.length);

      // Format packages for frontend
      const formattedPackages = packagesWithEMI.map(pkg => this.formatPackageForFrontend(pkg));
      
      return {
        success: true,
        matched_family_type: familyType,
        packages: formattedPackages,
        search_params: searchParams
      };
    } catch (error) {
      console.error('Database error in searchPackages:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Get package details
  async getPackageDetails(packageId) {
    try {
      console.log('📦 Fetching package details for ID:', packageId);
      
      const { data: packageData, error } = await this.quoteDB
        .from('family_type_prices')
        .select(`
          *,
          family_type_emi_plans(*)
        `)
        .eq('id', packageId)
        .single();
      
      if (error) {
        console.error('❌ Error fetching package details:', error);
        throw error;
      }
      
      if (!packageData) {
        throw new Error('Package not found');
      }
      
      console.log('✅ Loaded package details:', packageData.destination);
      
      const formattedPackage = this.formatPackageDetailsForFrontend(packageData);
      
      return { success: true, package: formattedPackage };
    } catch (error) {
      console.error('Database error in getPackageDetails:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Submit quote request
  async submitQuoteRequest(quoteData) {
    try {
      console.log('📝 Submitting quote request:', quoteData);
      
      // Insert into public_family_quotes table
      const { data, error } = await this.quoteDB
        .from('public_family_quotes')
        .insert({
          customer_email: quoteData.customer_email,
          customer_phone: quoteData.customer_phone,
          customer_name: quoteData.customer_name,
          destination: quoteData.destination,
          travel_date: quoteData.travel_date,
          no_of_adults: quoteData.adults,
          no_of_children: quoteData.children || 0,
          no_of_infants: quoteData.infants || 0,
          matched_price_id: quoteData.selected_package_id,
          selected_emi_plan_id: quoteData.selected_emi_plan_id,
          utm_source: this.getUtmSource(),
          session_id: this.sessionId,
          lead_source: 'family_website',
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        console.error('❌ Error submitting quote request:', error);
        throw error;
      }
      
      console.log('✅ Quote request submitted successfully:', data.id);
      
      return {
        success: true,
        quote_id: data.id,
        message: 'Quote request submitted successfully! Our team will contact you soon.'
      };
    } catch (error) {
      console.error('Database error in submitQuoteRequest:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Helper: Detect family type based on traveler counts
  async detectFamilyType(adults, children, infants) {
    try {
      const familyTypesResponse = await this.getFamilyTypes();
      
      if (!familyTypesResponse.success) {
        return this.getDefaultFamilyType(adults, children, infants);
      }
      
      const familyTypes = familyTypesResponse.data;
      
      // Find exact match first
      let match = familyTypes.find(ft => 
        ft.no_of_adults === adults && 
        ft.no_of_children === children && 
        ft.no_of_infants === infants
      );
      
      // If no exact match, find closest match
      if (!match) {
        match = familyTypes.find(ft => 
          ft.no_of_adults === adults && 
          ft.no_of_children >= children && 
          ft.no_of_infants >= infants
        );
      }
      
      // Default to first family type if no match
      if (!match) {
        match = familyTypes[0] || this.getDefaultFamilyType(adults, children, infants);
      }
      
      return match;
    } catch (error) {
      console.error('Error detecting family type:', error);
      return this.getDefaultFamilyType(adults, children, infants);
    }
  }
  
  // Helper: Get default family type
  getDefaultFamilyType(adults, children, infants) {
    return {
      family_id: 'CUSTOM',
      family_type: 'Custom Family',
      no_of_adults: adults,
      no_of_children: children,
      no_of_infants: infants,
      composition: this.formatFamilyComposition({ no_of_adults: adults, no_of_children: children, no_of_infants: infants })
    };
  }
  
  // Helper: Format family composition
  formatFamilyComposition(familyType) {
    let composition = `${familyType.no_of_adults} Adult${familyType.no_of_adults > 1 ? 's' : ''}`;
    
    if (familyType.no_of_children > 0) {
      composition += ` + ${familyType.no_of_children} Child${familyType.no_of_children > 1 ? 'ren' : ''}`;
    }
    
    if (familyType.no_of_infants > 0) {
      composition += ` + ${familyType.no_of_infants} Infant${familyType.no_of_infants > 1 ? 's' : ''}`;
    }
    
    return composition;
  }
  
  // Helper: Format package for frontend - Enhanced for Quote Generator data
  formatPackageForFrontend(packageData) {
    // Ensure we have a valid total price
    const totalPrice = packageData.total_price || packageData.total_cost || 45000;

    // Generate meaningful package title
    let packageTitle = packageData.package_title || packageData.title;
    if (!packageTitle) {
      const destination = packageData.destination || 'Travel';
      const packageType = this.getPackageTypeByPrice(totalPrice);
      const duration = packageData.package_duration_days || packageData.duration_days || 5;
      packageTitle = `${destination} ${packageType} Package (${duration}D/${duration + 1}N)`;
    }

    // Create EMI options (use existing or generate)
    let emiOptions = [];

    if (packageData.family_type_emi_plans && packageData.family_type_emi_plans.length > 0) {
      // Use existing EMI plans from database
      emiOptions = packageData.family_type_emi_plans.map(emi => ({
        id: emi.id || `emi-${Math.random().toString(36).substr(2, 9)}`,
        months: emi.emi_months || 6,
        monthly_amount: emi.monthly_amount || Math.round(totalPrice / (emi.emi_months || 6)),
        total_amount: emi.total_amount || totalPrice,
        processing_fee: emi.processing_fee || Math.round(totalPrice * 0.025),
        label: emi.marketing_label || `${emi.emi_months || 6} Months`,
        is_featured: emi.is_featured || false
      }));
    } else {
      // Generate default EMI options
      emiOptions = this.generateEMIOptions(totalPrice, packageData.id);
    }

    // Extract inclusions based on package data
    let inclusions = packageData.inclusions || [];
    if (inclusions.length === 0) {
      inclusions = this.generateInclusionsFromPackageData(packageData);
    }

    // Determine package category and offer badge
    const category = packageData.destination_category || this.getCategoryByDestination(packageData.destination);
    const offerBadge = this.getOfferBadge(totalPrice);

    return {
      id: packageData.id || `pkg-${Math.random().toString(36).substr(2, 9)}`,
      title: packageTitle,
      destination: packageData.destination || 'Unknown',
      duration_days: packageData.package_duration_days || packageData.duration_days || 5,
      total_price: totalPrice,
      family_type: packageData.family_type_name || packageData.family_type || 'Family Package',
      family_type_id: packageData.family_type_id,
      emi_options: emiOptions,
      inclusions: inclusions,
      images: [this.getDestinationImage(packageData.destination)], // Travel backdrop based on destination
      offer_badge: offerBadge,
      category: category,
      season: packageData.season_category || 'All Season',
      validity: this.getPackageValidity(packageData),
      is_public_visible: packageData.is_public_visible !== false,
      created_from: packageData.created_from || 'family_type_prices'
    };
  }

  // Helper: Generate inclusions from package data
  generateInclusionsFromPackageData(packageData) {
    const inclusions = [];

    // Basic inclusions
    inclusions.push('Accommodation');

    // Check for transportation
    if (packageData.vehicle_mappings || packageData.transport_cost > 0) {
      inclusions.push('Transportation');
    } else {
      inclusions.push('Local Transfers');
    }

    // Check for meals
    if (packageData.meal_cost_per_person > 0 || packageData.additional_costs?.meal_cost_per_person > 0) {
      inclusions.push('Meals');
    } else {
      inclusions.push('Breakfast');
    }

    // Standard inclusions
    inclusions.push('Sightseeing');

    // Additional based on price range
    if (packageData.total_price > 50000) {
      inclusions.push('Professional Guide');
    }

    if (packageData.total_price > 80000) {
      inclusions.push('Premium Hotels');
    }

    return inclusions;
  }

  // Helper: Get category by destination
  getCategoryByDestination(destination) {
    if (!destination) return 'General';

    const dest = destination.toLowerCase();
    if (dest.includes('kashmir') || dest.includes('manali') || dest.includes('shimla')) return 'Hill Station';
    if (dest.includes('goa') || dest.includes('andaman') || dest.includes('kerala')) return 'Beach';
    if (dest.includes('rajasthan') || dest.includes('jaipur') || dest.includes('udaipur')) return 'Heritage';
    if (dest.includes('ladakh') || dest.includes('spiti') || dest.includes('adventure')) return 'Adventure';

    return 'Popular';
  }

  // Helper: Get package validity
  getPackageValidity(packageData) {
    if (packageData.travel_date) {
      return `Valid for travel: ${new Date(packageData.travel_date).toLocaleDateString()}`;
    }
    if (packageData.created_at) {
      const validUntil = new Date(packageData.created_at);
      validUntil.setMonth(validUntil.getMonth() + 6); // Valid for 6 months
      return `Valid until: ${validUntil.toLocaleDateString()}`;
    }
    return 'Valid for booking';
  }

  // Helper: Get destination-specific image
  getDestinationImage(destination) {
    if (!destination) return 'img/rectangle-14.png';

    const dest = destination.toLowerCase();

    // Map destinations to available images
    if (dest.includes('kashmir') || dest.includes('manali') || dest.includes('hill')) {
      return 'img/rectangle-14.png'; // Mountain/Hill station image
    }
    if (dest.includes('goa') || dest.includes('beach') || dest.includes('andaman')) {
      return 'img/rectangle-14-2.png'; // Beach image
    }
    if (dest.includes('rajasthan') || dest.includes('heritage') || dest.includes('palace')) {
      return 'img/rectangle-14-4.png'; // Heritage image
    }

    // Default travel image
    return 'img/rectangle-14.png';
  }
  
  // Helper: Format package details for frontend
  formatPackageDetailsForFrontend(packageData) {
    const basePackage = this.formatPackageForFrontend(packageData);
    
    return {
      ...basePackage,
      description: `Experience the beauty of ${packageData.destination} with our carefully crafted family package.`,
      itinerary: [
        {
          day: 1,
          title: `Arrival in ${packageData.destination}`,
          description: 'Airport pickup, hotel check-in, welcome dinner'
        },
        {
          day: 2,
          title: 'Sightseeing Tour',
          description: 'Visit famous attractions and local markets'
        },
        {
          day: 3,
          title: 'Adventure Activities',
          description: 'Enjoy adventure sports and outdoor activities'
        }
      ],
      inclusions: [
        'Round-trip flights',
        '4-star hotel accommodation',
        'Daily breakfast and dinner',
        'All sightseeing and transfers',
        'Professional guide'
      ],
      exclusions: [
        'Personal expenses',
        'Travel insurance',
        'Lunch (except on specified days)',
        'Tips and gratuities'
      ]
    };
  }
  
  // Helper: Get UTM source
  getUtmSource() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('utm_source') || 'direct';
  }

  // Helper: Create sample packages when database is empty
  createSamplePackages(destination, familyType, searchParams) {
    console.log('🎨 Creating sample packages for:', destination);

    const samplePackages = [
      {
        id: 'sample-1',
        title: `${destination} Family Package`,
        destination: destination,
        duration_days: 5,
        total_price: 45000,
        family_type: familyType.family_type,
        emi_options: [
          {
            id: 'emi-1',
            months: 3,
            monthly_amount: 15000,
            total_amount: 45000,
            processing_fee: 1000,
            label: 'Quick Pay',
            is_featured: false
          },
          {
            id: 'emi-2',
            months: 6,
            monthly_amount: 7500,
            total_amount: 45000,
            processing_fee: 1500,
            label: 'Best Value',
            is_featured: true
          },
          {
            id: 'emi-3',
            months: 12,
            monthly_amount: 3750,
            total_amount: 45000,
            processing_fee: 2000,
            label: 'Low Monthly',
            is_featured: false
          }
        ],
        inclusions: ['Flights', 'Hotels', 'Meals', 'Sightseeing'],
        images: [this.getDestinationImage(destination)],
        offer_badge: '15% OFF'
      },
      {
        id: 'sample-2',
        title: `${destination} Adventure Package`,
        destination: destination,
        duration_days: 7,
        total_price: 65000,
        family_type: familyType.family_type,
        emi_options: [
          {
            id: 'emi-4',
            months: 6,
            monthly_amount: 10833,
            total_amount: 65000,
            processing_fee: 2000,
            label: 'Popular',
            is_featured: true
          }
        ],
        inclusions: ['Flights', 'Hotels', 'Meals', 'Adventure Activities'],
        images: [this.getDestinationImage(destination)],
        offer_badge: 'Best Value'
      }
    ];

    return {
      success: true,
      matched_family_type: familyType,
      packages: samplePackages,
      search_params: searchParams
    };
  }

  // Helper: Convert quote mapping to package format
  convertQuoteMappingToPackage(mapping, familyType) {
    const quote = mapping.quotes;
    const packageName = mapping.quote_name || `${mapping.destination} Package`;
    const totalPrice = quote?.total_cost || quote?.subtotal || 45000;

    return {
      id: mapping.id,
      title: packageName,
      destination: mapping.destination,
      duration_days: quote?.duration_days || 5,
      total_price: totalPrice,
      family_type: familyType.family_type,
      emi_options: this.generateEMIOptions(totalPrice, mapping.id),
      inclusions: this.extractInclusions(mapping),
      images: [this.getDestinationImage(mapping.destination)], // Destination-specific image
      offer_badge: this.getOfferBadge(totalPrice),
      package_validity: quote?.travel_date || 'Valid for booking',
      created_from: 'quote_mapping'
    };
  }

  // Helper: Convert quote to package format
  convertQuoteToPackage(quote, familyType) {
    const packageName = `${quote.destination} ${this.getPackageTypeByPrice(quote.total_cost)} Package`;
    const totalPrice = quote.total_cost || quote.subtotal || 45000;

    return {
      id: quote.id,
      title: packageName,
      destination: quote.destination,
      duration_days: quote.duration_days || 5,
      total_price: totalPrice,
      family_type: familyType.family_type,
      emi_options: this.generateEMIOptions(totalPrice, quote.id),
      inclusions: ['Flights', 'Hotels', 'Meals', 'Sightseeing'],
      images: [this.getDestinationImage(quote.destination)], // Destination-specific image
      offer_badge: this.getOfferBadge(totalPrice),
      package_validity: quote.travel_date || 'Valid for booking',
      created_from: 'quote'
    };
  }

  // Helper: Generate EMI options based on price
  generateEMIOptions(totalPrice, packageId) {
    return [
      {
        id: `emi-3-${packageId}`,
        months: 3,
        monthly_amount: Math.round(totalPrice / 3),
        total_amount: totalPrice,
        processing_fee: Math.round(totalPrice * 0.02),
        label: 'Quick Pay',
        is_featured: false
      },
      {
        id: `emi-6-${packageId}`,
        months: 6,
        monthly_amount: Math.round(totalPrice / 6),
        total_amount: totalPrice,
        processing_fee: Math.round(totalPrice * 0.03),
        label: 'Best Value',
        is_featured: true
      },
      {
        id: `emi-12-${packageId}`,
        months: 12,
        monthly_amount: Math.round(totalPrice / 12),
        total_amount: totalPrice,
        processing_fee: Math.round(totalPrice * 0.05),
        label: 'Low Monthly',
        is_featured: false
      }
    ];
  }

  // Helper: Extract inclusions from quote mapping
  extractInclusions(mapping) {
    const inclusions = ['Accommodation'];

    // Check hotel mappings
    if (mapping.hotel_mappings && mapping.hotel_mappings.length > 0) {
      inclusions.push('Hotels');
    }

    // Check vehicle mappings
    if (mapping.vehicle_mappings && mapping.vehicle_mappings.length > 0) {
      inclusions.push('Transportation');
    }

    // Check additional costs
    if (mapping.additional_costs) {
      const costs = mapping.additional_costs;
      if (costs.meal_cost_per_person > 0) inclusions.push('Meals');
      if (costs.activity_cost_per_person > 0) inclusions.push('Activities');
      if (costs.guide_cost_per_day > 0) inclusions.push('Guide');
    }

    // Default inclusions if none found
    if (inclusions.length === 1) {
      inclusions.push('Sightseeing', 'Transfers');
    }

    return inclusions;
  }

  // Helper: Get package type based on price
  getPackageTypeByPrice(price) {
    if (price < 30000) return 'Budget';
    if (price < 60000) return 'Standard';
    if (price < 100000) return 'Premium';
    return 'Luxury';
  }

  // Helper: Get offer badge based on price
  getOfferBadge(price) {
    if (price > 80000) return '15% OFF';
    if (price > 50000) return '10% OFF';
    if (price > 30000) return 'Best Value';
    return 'Great Deal';
  }
}

// Create global database service instance
const databaseService = new DatabaseService();

// Export for use in other scripts
window.databaseService = databaseService;

console.log('🚀 Database Service loaded successfully');
