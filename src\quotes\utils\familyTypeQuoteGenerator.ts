// Family Type Price Generator for Quote Generator Integration
import { getQuoteClient, getCrmClient } from '../../lib/supabaseManager';

// =============================================================================
// INTERFACES - Using exact Quote Generator data structures
// =============================================================================

export interface FamilyTypeDB {
  family_id: string;
  family_type: string;
  no_of_adults: number;
  no_of_infants: number;     // Below 2 yrs - Free
  no_of_child: number;       // Below 5 yrs - Free  
  no_of_children: number;    // 6-12 yrs - Charged
  family_count: number;      // Total family members
  cab_type: string;          // Required vehicle type
  cab_capacity: number;      // Vehicle capacity
  rooms_need: number;        // Rooms required
}

export interface QuoteGeneratorData {
  // Basic quote info
  packageName: string;
  customerName: string;
  destination: string;
  quoteDate: string;
  validityDate: string;
  noOfPersons: number;
  extraAdults: number;
  children: number;
  infants: number;
  
  // Hotel data
  hotelRows: HotelRow[];
  
  // Costs
  costs: {
    basicCosts: {
      meals: number;
      transportation: number;
      cabSightseeing: number;
      trainCost: number;
      ferryCost: number;
      parkingToll: number;
    };
    addOnCosts: {
      addOnActivity: number;
      marketing: number;
      addOn: number;
    };
    optionalCosts: {
      flightTicket: number;
      guideWages: number;
    };
  };
  
  // Quote settings
  commission: number;
  discountValue: number;
  discountType: string;
}

export interface HotelRow {
  hotelName: string;
  roomType: string;
  price: number;
  mealPlan: string;
  noOfRooms: number;
  stayNights: number;
  extraAdultCost: number;
  childrenCost: number;
  infantCost: number;
  gstType: string;
  tacPercentage: number;
}

export interface QuoteMappingData {
  id: string;
  quote_id: string;
  hotel_mappings: HotelCostMapping[];
  vehicle_mappings: VehicleCostMapping[];
  additional_costs: {
    meal_cost_per_person: number;
    ferry_cost: number;
    activity_cost_per_person: number;
    guide_cost_per_day: number;
    parking_toll_multiplier: number;
  };
}

export interface HotelCostMapping {
  hotel_name: string;
  extra_adult_cost: number;
  children_cost: number;
  infant_cost: number;
  grandparent_discount: number;
}

export interface VehicleCostMapping {
  vehicle_type: string;
  pricing_type: 'multiplier' | 'actual_cost';
  base_cost: number;
  cost_multiplier: number;
  max_capacity: number;
  is_active: boolean;
}

export interface FamilyTypePriceResult {
  familyType: FamilyTypeDB;
  calculatedPrice: number;
  breakdown: {
    hotelCost: number;
    vehicleCost: number;
    additionalCosts: number;
    basicCosts: number;
    addOnCosts: number;
    optionalCosts: number;
    subtotal: number;
    discount: number;
    commission: number;
    grandTotal: number;
    rooms: number;
    extraAdults: number;
    childrenCharged: number;
    infantsFree: number;
    roomType?: string;
    notes: string[];
  };
}

// =============================================================================
// CORE FUNCTIONS
// =============================================================================

// Step 1: Validate Quote Mapping Data
export const validateQuoteMappingData = async (quoteId?: string): Promise<{ isValid: boolean; data?: QuoteMappingData; message: string }> => {
  try {
    if (!quoteId) {
      return { isValid: false, message: 'No quote ID provided. Please save the quote first.' };
    }

    const supabase = await getQuoteClient();
    if (!supabase) {
      return { isValid: false, message: 'Database connection not available.' };
    }

    // Check if Quote Mapping exists
    const { data, error } = await supabase
      .from('quote_mappings')
      .select('*')
      .eq('quote_id', quoteId)
      .single();

    if (error || !data) {
      return { 
        isValid: false, 
        message: 'Quote Mapping data not found. Please go to Quote Mapping tab and configure hotel rates, vehicle rates, and additional costs first.' 
      };
    }

    // Validate required mapping data
    const issues: string[] = [];

    if (!data.hotel_mappings || data.hotel_mappings.length === 0) {
      issues.push('Hotel cost mappings are missing');
    }

    if (!data.vehicle_mappings || data.vehicle_mappings.length === 0) {
      issues.push('Vehicle cost mappings are missing');
    }

    if (!data.additional_costs) {
      issues.push('Additional cost configurations are missing');
    }

    if (issues.length > 0) {
      return {
        isValid: false,
        message: `Quote Mapping incomplete:\n• ${issues.join('\n• ')}\n\nPlease complete the Quote Mapping configuration.`
      };
    }

    return { isValid: true, data, message: 'Quote Mapping data is valid and complete.' };

  } catch (error) {
    return { 
      isValid: false, 
      message: `Error validating Quote Mapping: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
};

// Step 2: Get Family Types from Database
export const getFamilyTypesFromDatabase = async (): Promise<FamilyTypeDB[]> => {
  try {
    const supabase = await getCrmClient();
    if (!supabase) {
      console.error('❌ CRM client not available');
      return [];
    }

    const { data, error } = await supabase
      .from('family_type')
      .select('*')
      .order('family_id');

    if (error) {
      console.error('❌ Error fetching family types:', error);
      return [];
    }

    console.log(`✅ Fetched ${data?.length || 0} family types from database`);
    return data || [];
  } catch (error) {
    console.error('❌ Exception getting family types:', error);
    return [];
  }
};

// =============================================================================
// MAIN FUNCTION: Generate Family Type Prices
// =============================================================================

// Save Family Type Prices to Database
export const saveFamilyTypePricesToDatabase = async (
  quoteId: string, // This should be UUID string from the quotes table
  results: FamilyTypePriceResult[],
  quoteGeneratorData: QuoteGeneratorData,
  quoteMappingData: QuoteMappingData
): Promise<{ success: boolean; message: string }> => {
  try {
    const supabase = await getQuoteClient();
    if (!supabase) {
      return { success: false, message: 'Database connection not available.' };
    }

    console.log(`💾 Saving ${results.length} family type prices to database...`);

    // Prepare data for insertion
    const familyTypePricesData = results.map(result => ({
      quote_id: quoteId,
      family_type_id: result.familyType.family_id,
      family_type_name: result.familyType.family_type,
      
      // Family Composition
      no_of_adults: result.familyType.no_of_adults,
      no_of_children: result.familyType.no_of_children,
      no_of_child: result.familyType.no_of_child,
      no_of_infants: result.familyType.no_of_infants,
      family_count: result.familyType.family_count,
      
      // Room and Vehicle Info
      rooms_need: result.familyType.rooms_need,
      cab_type: result.familyType.cab_type,
      cab_capacity: result.familyType.cab_capacity,
      
      // Calculated Costs
      hotel_cost: result.breakdown.hotelCost,
      vehicle_cost: result.breakdown.vehicleCost,
      additional_costs: result.breakdown.additionalCosts,
      basic_costs: result.breakdown.basicCosts,
      addon_costs: result.breakdown.addOnCosts,
      optional_costs: result.breakdown.optionalCosts,
      
      // Final Pricing
      subtotal: result.breakdown.subtotal,
      discount_amount: result.breakdown.discount,
      commission_amount: result.breakdown.commission,
      total_price: result.breakdown.grandTotal,
      
      // Room Calculation Details
      extra_adults: result.breakdown.extraAdults,
      children_charged: result.breakdown.childrenCharged,
      infants_free: result.breakdown.infantsFree,
      room_type: result.breakdown.roomType || null,
      
      // Metadata
      baseline_quote_data: {
        packageName: quoteGeneratorData.packageName,
        customerName: quoteGeneratorData.customerName,
        destination: quoteGeneratorData.destination,
        quoteDate: quoteGeneratorData.quoteDate,
        validityDate: quoteGeneratorData.validityDate,
        noOfPersons: quoteGeneratorData.noOfPersons,
        extraAdults: quoteGeneratorData.extraAdults,
        children: quoteGeneratorData.children,
        infants: quoteGeneratorData.infants,
        commission: quoteGeneratorData.commission,
        discountValue: quoteGeneratorData.discountValue,
        discountType: quoteGeneratorData.discountType
      },
      quote_mapping_data: quoteMappingData,
      calculation_notes: result.breakdown.notes || []
    }));

    // Delete existing records for this quote (to replace with new calculations)
    const { error: deleteError } = await supabase
      .from('family_type_prices')
      .delete()
      .eq('quote_id', quoteId);

    if (deleteError) {
      console.warn('⚠️ Warning: Could not delete existing family type prices:', deleteError);
      // Continue anyway - might be first time saving
    }

    // Insert new records
    const { error: insertError } = await supabase
      .from('family_type_prices')
      .insert(familyTypePricesData);

    if (insertError) {
      console.error('❌ Error saving family type prices:', insertError);
      return { success: false, message: `Failed to save family type prices: ${insertError.message}` };
    }

    console.log(`✅ Successfully saved ${results.length} family type prices to database`);
    return { success: true, message: `Successfully saved ${results.length} family type prices to database.` };

  } catch (error) {
    console.error('❌ Exception saving family type prices:', error);
    return { success: false, message: `Error saving family type prices: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
};

// Load Family Type Prices from Database
export const loadFamilyTypePricesFromDatabase = async (
  quoteId: string
): Promise<{ success: boolean; results?: FamilyTypePriceResult[]; message: string }> => {
  try {
    const supabase = await getQuoteClient();
    if (!supabase) {
      return { success: false, message: 'Database connection not available.' };
    }

    console.log(`📖 Loading family type prices for quote: ${quoteId}`);

    const { data, error } = await supabase
      .from('family_type_prices')
      .select('*')
      .eq('quote_id', quoteId)
      .order('family_type_name');

    if (error) {
      console.error('❌ Error loading family type prices:', error);
      return { success: false, message: `Failed to load family type prices: ${error.message}` };
    }

    if (!data || data.length === 0) {
      return { success: false, message: 'No saved family type prices found for this quote.' };
    }

    // Convert database data back to FamilyTypePriceResult format
    const results: FamilyTypePriceResult[] = data.map(row => ({
      familyType: {
        family_id: row.family_type_id,
        family_type: row.family_type_name,
        no_of_adults: row.no_of_adults,
        no_of_children: row.no_of_children,
        no_of_child: row.no_of_child,
        no_of_infants: row.no_of_infants,
        family_count: row.family_count,
        rooms_need: row.rooms_need,
        cab_type: row.cab_type,
        cab_capacity: row.cab_capacity
      },
      calculatedPrice: row.total_price,
      breakdown: {
        hotelCost: row.hotel_cost,
        vehicleCost: row.vehicle_cost,
        additionalCosts: row.additional_costs,
        basicCosts: row.basic_costs,
        addOnCosts: row.addon_costs,
        optionalCosts: row.optional_costs,
        subtotal: row.subtotal,
        discount: row.discount_amount,
        commission: row.commission_amount,
        grandTotal: row.total_price,
        rooms: row.rooms_need,
        extraAdults: row.extra_adults,
        childrenCharged: row.children_charged,
        infantsFree: row.infants_free,
        roomType: row.room_type,
        notes: row.calculation_notes || []
      }
    }));

    console.log(`✅ Loaded ${results.length} family type prices from database`);
    return { success: true, results, message: `Successfully loaded ${results.length} family type prices from database.` };

  } catch (error) {
    console.error('❌ Exception loading family type prices:', error);
    return { success: false, message: `Error loading family type prices: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
};

export const generateFamilyTypePrices = async (
  quoteGeneratorData: QuoteGeneratorData,
  currentQuoteId?: string
): Promise<{ success: boolean; results?: FamilyTypePriceResult[]; message: string }> => {
  
  console.log('\n🎯 === FAMILY TYPE PRICE GENERATION ===');
  console.log(`📊 Quote: ${quoteGeneratorData.customerName} - ${quoteGeneratorData.destination}`);

  try {
    // Step 1: Validate Quote Mapping
    const validation = await validateQuoteMappingData(currentQuoteId);
    if (!validation.isValid) {
      return { success: false, message: validation.message };
    }

    const quoteMappingData = validation.data!;
    console.log('✅ Quote Mapping validation passed');

    // Step 2: Get Family Types
    const familyTypes = await getFamilyTypesFromDatabase();
    if (familyTypes.length === 0) {
      return { success: false, message: 'No family types found in database.' };
    }

    console.log(`✅ Processing ${familyTypes.length} family types`);

    // Step 3: Calculate prices for each family type
    const results: FamilyTypePriceResult[] = [];

    for (const familyType of familyTypes) {
      try {
        console.log(`\n💰 Calculating: ${familyType.family_type}`);
        
        // Use Quote Generator calculation logic here
        const calculatedPrice = calculatePriceForFamilyType(familyType, quoteGeneratorData, quoteMappingData);
        
        const result: FamilyTypePriceResult = {
          familyType,
          calculatedPrice: calculatedPrice.grandTotal,
          breakdown: calculatedPrice
        };

        results.push(result);
        console.log(`✅ ${familyType.family_type}: ₹${calculatedPrice.grandTotal.toLocaleString()}`);

      } catch (error) {
        console.error(`❌ Error calculating ${familyType.family_type}:`, error);
      }
    }

    console.log(`\n🎉 === GENERATION COMPLETE ===`);
    console.log(`✅ Generated prices for ${results.length}/${familyTypes.length} family types`);

    // Step 4: Save to Database (if quote ID is available)
    if (currentQuoteId && results.length > 0) {
      const saveResult = await saveFamilyTypePricesToDatabase(currentQuoteId, results, quoteGeneratorData, quoteMappingData);
      if (saveResult.success) {
        console.log('✅ Family type prices saved to database');
      } else {
        console.warn('⚠️ Could not save to database:', saveResult.message);
      }
    }

    return { 
      success: true, 
      results, 
      message: `Successfully generated exact prices for ${results.length} family types using Quote Mapping data.` 
    };

  } catch (error) {
    console.error('❌ Error in family type price generation:', error);
    return { 
      success: false, 
      message: `Error generating family type prices: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
};

// Calculate Room Requirements following hotel industry standards
const calculateRoomRequirements = (familyType: FamilyTypeDB) => {
  const adults = familyType.no_of_adults;
  const childrenFree = familyType.no_of_child;      // Below 5 yrs - Free
  const childrenCharged = familyType.no_of_children; // 6-12 yrs - Charged  
  const infantsFree = familyType.no_of_infants;     // Below 2 yrs - Free
  const roomsFromDB = familyType.rooms_need;        // Use DB value as authoritative

  console.log(`🏨 Room Calculation for ${familyType.family_type}:`);
  console.log(`   👥 Adults: ${adults}, Children (6-12): ${childrenCharged}, Children (≤5): ${childrenFree}, Infants: ${infantsFree}`);
  console.log(`   🏠 DB Rooms Need: ${roomsFromDB}`);

  // Use database rooms_need as the authoritative room count
  const roomsNeeded = roomsFromDB || 1;
  
  /*
   * Hotel Room Calculation Logic:
   * - Standard Room: 2 Adults + 1 Extra Adult = 3 Adults max
   * - Family Room: 4 Adults + 1 Extra Adult = 5 Adults max
   * - Triple Room: 3 Adults + 1 Extra Adult = 4 Adults max
   * 
   * Children ≤5 and infants are free and don't count towards adult capacity
   * Children 6-12 are charged but don't count as adults for room capacity
   */
  
  let extraAdults = 0;
  let roomType = "Standard";
  
  if (roomsNeeded === 1) {
    // Single room - determine if it's Standard, Triple, or Family Room
    if (adults <= 2) {
      roomType = "Standard Room (2 Adults + 1 Extra)";
      extraAdults = Math.max(0, adults - 2); // 0 extra if 2 or fewer adults
    } else if (adults === 3) {
      roomType = "Standard Room (3 Adults)";
      extraAdults = 1; // 1 extra adult beyond base 2
    } else if (adults === 4) {
      roomType = "Family Room (4 Adults)";
      extraAdults = 0; // Family room base capacity is 4
    } else if (adults === 5) {
      roomType = "Family Room (4 Adults + 1 Extra)";
      extraAdults = 1; // 1 extra beyond family room base
    } else {
      // More than 5 adults in 1 room - use family room + extras
      roomType = "Family Room (4+ Adults)";
      extraAdults = adults - 4; // All beyond 4 are extras
    }
  } else {
    // Multiple rooms - distribute adults evenly
    const adultsPerRoom = adults / roomsNeeded;
    
    if (adultsPerRoom <= 2) {
      roomType = `${roomsNeeded} Standard Rooms`;
      extraAdults = 0; // No extras needed
    } else if (adultsPerRoom <= 3) {
      roomType = `${roomsNeeded} Standard Rooms`;
      extraAdults = adults - (roomsNeeded * 2); // Extras beyond 2 per room
    } else {
      roomType = `${roomsNeeded} Mixed Rooms`;
      // Calculate extras based on optimal room distribution
      const standardRooms = Math.floor(adults / 3); // 3 adults per standard room
      const remainingAdults = adults % 3;
      extraAdults = Math.min(adults - (roomsNeeded * 2), roomsNeeded); // Max 1 extra per room
    }
  }
  
  const notes: string[] = [];
  notes.push(`${roomsNeeded} rooms (${roomType})`);
  notes.push(`${adults} adults total`);
  
  if (extraAdults > 0) {
    notes.push(`${extraAdults} extra adult charges`);
  }
  if (childrenCharged > 0) {
    notes.push(`${childrenCharged} children (6-12 yrs) charged`);
  }
  if (childrenFree > 0 || infantsFree > 0) {
    notes.push(`${childrenFree + infantsFree} children/infants free`);
  }

  console.log(`   🏨 Result: ${roomsNeeded} rooms, ${extraAdults} extra adults`);
  console.log(`   📝 Room Type: ${roomType}`);

  return {
    roomsNeeded,
    extraAdults,
    childrenCharged,
    infantsFree: infantsFree + childrenFree,
    roomType,
    notes
  };
};

// Calculate Hotel Costs using Quote Mapping data and proper room logic
const calculateHotelCosts = (
  familyType: FamilyTypeDB,
  roomReq: any,
  quoteData: QuoteGeneratorData,
  mappingData: QuoteMappingData
) => {
  console.log(`🏨 Hotel Cost Calculation for ${familyType.family_type}:`);
  console.log(`   🏠 Room Requirement: ${roomReq.roomType}`);
  console.log(`   👥 Extra Adults: ${roomReq.extraAdults}, Children (6-12): ${roomReq.childrenCharged}, Free Children/Infants: ${roomReq.infantsFree}`);

  // Check if we have hotel rows data
  if (!quoteData.hotelRows || quoteData.hotelRows.length === 0) {
    console.log(`   ⚠️ No hotel rows found, using fallback calculation`);

    // Fallback calculation when no hotel data is available
    const totalTransportCost = quoteData.costs.basicCosts.transportation + quoteData.costs.basicCosts.cabSightseeing;
    const estimatedHotelBudget = totalTransportCost * 1.6; // Hotel ~1.6x transportation
    const nights = 3; // Default nights
    const baseRoomCostPerNight = Math.max(1500, estimatedHotelBudget / (roomReq.roomsNeeded * nights));

    console.log(`   💰 Estimated hotel rate: ₹${baseRoomCostPerNight}/night (from transport cost)`);

    const baseRoomCost = baseRoomCostPerNight * roomReq.roomsNeeded * nights;

    // Extra costs using Quote Mapping data
    const hotelMapping = mappingData.hotel_mappings?.[0] || {
      extra_adult_cost: 500,    // Default extra adult cost per night
      children_cost: 300,       // Default children (6-12) cost per night
      infant_cost: 0            // Infants and children ≤5 are free
    };

    const extraAdultCost = hotelMapping.extra_adult_cost * roomReq.extraAdults * nights;
    const childrenCost = hotelMapping.children_cost * roomReq.childrenCharged * nights;
    const infantCost = hotelMapping.infant_cost * roomReq.infantsFree * nights;

    const totalHotelCost = baseRoomCost + extraAdultCost + childrenCost + infantCost;
    console.log(`   ✅ Fallback Total Hotel Cost: ₹${totalHotelCost}`);

    return Math.round(totalHotelCost);
  }

  console.log(`   🏨 Processing ${quoteData.hotelRows.length} hotels from Quote Generator`);

  let totalHotelCost = 0;
  let totalNights = 0;

  // Process each hotel row from Quote Generator
  for (let i = 0; i < quoteData.hotelRows.length; i++) {
    const hotelRow = quoteData.hotelRows[i];
    const hotelNights = hotelRow.stayNights || 0;
    const hotelRooms = hotelRow.noOfRooms || 0;
    const hotelPrice = hotelRow.price || 0;

    console.log(`   🏨 Hotel ${i + 1}: ${hotelRow.hotelName || 'Unnamed'}`);
    console.log(`      💰 Rate: ₹${hotelPrice}/night, Rooms: ${hotelRooms}, Nights: ${hotelNights}`);

    // Skip hotels with no rooms or nights
    if (hotelRooms === 0 || hotelNights === 0) {
      console.log(`      ⏭️ Skipping hotel with 0 rooms or nights`);
      continue;
    }

    // Calculate base room cost for this hotel
    // Use family's room requirement but respect the hotel's room configuration
    const familyRoomsForThisHotel = Math.min(roomReq.roomsNeeded, hotelRooms);
    const baseRoomCostForHotel = hotelPrice * familyRoomsForThisHotel * hotelNights;

    console.log(`      🏠 Base cost: ₹${hotelPrice} × ${familyRoomsForThisHotel} rooms × ${hotelNights} nights = ₹${baseRoomCostForHotel}`);

    // Find hotel-specific mapping or use default
    const hotelMapping = mappingData.hotel_mappings?.find(hm =>
      hm.hotel_name.toLowerCase().includes(hotelRow.hotelName?.toLowerCase() || '')
    ) || mappingData.hotel_mappings?.[0] || {
      extra_adult_cost: hotelRow.extraAdultCost || 500,
      children_cost: hotelRow.childrenCost || 300,
      infant_cost: hotelRow.infantCost || 0
    };

    console.log(`      💰 Extra costs: Adults ₹${hotelMapping.extra_adult_cost}/night, Children ₹${hotelMapping.children_cost}/night`);

    // Calculate extra costs for this hotel
    const extraAdultCostForHotel = hotelMapping.extra_adult_cost * roomReq.extraAdults * hotelNights;
    const childrenCostForHotel = hotelMapping.children_cost * roomReq.childrenCharged * hotelNights;
    const infantCostForHotel = hotelMapping.infant_cost * roomReq.infantsFree * hotelNights;

    const hotelTotalCost = baseRoomCostForHotel + extraAdultCostForHotel + childrenCostForHotel + infantCostForHotel;

    console.log(`      ✅ Hotel ${i + 1} Total: ₹${baseRoomCostForHotel} + ₹${extraAdultCostForHotel} + ₹${childrenCostForHotel} + ₹${infantCostForHotel} = ₹${hotelTotalCost}`);

    totalHotelCost += hotelTotalCost;
    totalNights += hotelNights;
  }

  console.log(`   🌙 Total nights across all hotels: ${totalNights}`);
  console.log(`   ✅ Grand Total Hotel Cost: ₹${totalHotelCost}`);

  return Math.round(totalHotelCost);
};

// Calculate Vehicle Costs using Database cab_type and Quote Mapping data
const calculateVehicleCosts = (
  familyType: FamilyTypeDB,
  quoteData: QuoteGeneratorData,
  mappingData: QuoteMappingData
) => {
  const cabType = familyType.cab_type; // Use exact DB value
  const cabCapacity = familyType.cab_capacity;
  const familyCount = familyType.family_count;
  
  console.log(`🚗 Vehicle Calculation for ${familyType.family_type}:`);
  console.log(`   🚙 Required: ${cabType} (${cabCapacity} capacity)`);
  console.log(`   👥 Family Count: ${familyCount} people`);
  
  // Base transportation cost from Quote Generator
  const baseTransportationCost = quoteData.costs.basicCosts.transportation + 
                                 quoteData.costs.basicCosts.cabSightseeing;
  
  console.log(`   💰 Base Transportation Cost: ₹${baseTransportationCost}`);
  
  // Find matching vehicle from Quote Mapping using exact cab_type
  const vehicleMapping = mappingData.vehicle_mappings?.find(vm => {
    if (!vm.is_active) return false;
    
    // Match by vehicle type name
    const mappingType = vm.vehicle_type.toLowerCase();
    const familyCabType = cabType.toLowerCase();
    
    // Direct name matching
    if (mappingType.includes('sedan') && familyCabType.includes('sedan')) return true;
    if (mappingType.includes('innova') && familyCabType.includes('innova')) return true;
    if (mappingType.includes('crysta') && familyCabType.includes('innova')) return true;
    if (mappingType.includes('suv') && familyCabType.includes('suv')) return true;
    if (mappingType.includes('scorpio') && familyCabType.includes('suv')) return true;
    if (mappingType.includes('tempo') && familyCabType.includes('tempo')) return true;
    if (mappingType.includes('bus') && familyCabType.includes('bus')) return true;
    
    // Capacity matching as fallback
    return vm.max_capacity >= familyCount;
  });
  
  let vehicleCost = baseTransportationCost;
  let vehicleMultiplier = 1.0;
  
  if (vehicleMapping) {
    console.log(`   ✅ Found Quote Mapping: ${vehicleMapping.vehicle_type} (${vehicleMapping.max_capacity} capacity)`);
    
    if (vehicleMapping.pricing_type === 'actual_cost') {
      vehicleCost = vehicleMapping.base_cost;
      console.log(`   💰 Using Actual Cost: ₹${vehicleCost}`);
    } else {
      vehicleMultiplier = vehicleMapping.cost_multiplier;
      vehicleCost = baseTransportationCost * vehicleMultiplier;
      console.log(`   💰 Using Multiplier: ${vehicleMultiplier}x = ₹${vehicleCost}`);
    }
  } else {
    console.log(`   ⚠️ No Quote Mapping found, using fallback multipliers`);
    
    // Fallback multipliers based on database cab_type
    const cabTypeLower = cabType.toLowerCase();
    if (cabTypeLower.includes('sedan')) {
      vehicleMultiplier = 1.0; // Base rate
    } else if (cabTypeLower.includes('innova') || cabTypeLower.includes('crysta')) {
      vehicleMultiplier = 1.2; // 20% more than sedan
    } else if (cabTypeLower.includes('suv') || cabTypeLower.includes('scorpio')) {
      vehicleMultiplier = 1.25; // 25% more than sedan
    } else if (cabTypeLower.includes('tempo')) {
      vehicleMultiplier = 1.4; // 40% more than sedan
    } else if (cabTypeLower.includes('bus')) {
      vehicleMultiplier = 1.6; // 60% more than sedan
    }
    
    vehicleCost = baseTransportationCost * vehicleMultiplier;
    console.log(`   💰 Fallback Multiplier: ${vehicleMultiplier}x = ₹${vehicleCost}`);
  }
  
  // Validate vehicle capacity vs family count
  const capacityWarning = cabCapacity < familyCount;
  if (capacityWarning) {
    console.log(`   ⚠️ WARNING: Vehicle capacity (${cabCapacity}) < Family count (${familyCount})`);
    // Increase cost for capacity mismatch (may need larger vehicle or multiple vehicles)
    vehicleCost *= 1.2;
    console.log(`   💰 Adjusted for capacity mismatch: ₹${vehicleCost}`);
  }
  
  console.log(`   ✅ Final Vehicle Cost: ₹${Math.round(vehicleCost)}`);
  
  return Math.round(vehicleCost);
};

// Calculate Additional Costs using Quote Mapping data
const calculateAdditionalCosts = (
  familyType: FamilyTypeDB,
  quoteData: QuoteGeneratorData,
  mappingData: QuoteMappingData
) => {
  const familyCount = familyType.family_count;
  const baselinePeople = quoteData.noOfPersons + quoteData.extraAdults +
                        quoteData.children + quoteData.infants;

  // Scale by people ratio
  const peopleRatio = familyCount / Math.max(baselinePeople, 1);

  console.log(`💰 Additional Costs Calculation for ${familyType.family_type}:`);
  console.log(`   👥 Family Count: ${familyCount} people`);
  console.log(`   🧮 Adults: ${familyType.no_of_adults}, Children (≤5): ${familyType.no_of_child}, Children (6-12): ${familyType.no_of_children}, Infants: ${familyType.no_of_infants}`);

  // Use Quote Mapping additional costs if available
  let additionalCosts = 0;
  if (mappingData.additional_costs) {
    // Meal cost per person (all family members)
    const mealCosts = mappingData.additional_costs.meal_cost_per_person * familyCount;

    // Ferry cost calculation: multiply by total persons excluding infants
    // Ferry cost should be: ferry_cost * (no_of_adults + no_of_child + no_of_children)
    const ferryEligiblePersons = familyType.no_of_adults + familyType.no_of_child + familyType.no_of_children;
    const ferryCosts = mappingData.additional_costs.ferry_cost * ferryEligiblePersons;

    // Activity cost per person (all family members)
    const activityCosts = mappingData.additional_costs.activity_cost_per_person * familyCount;

    // Guide cost per day (fixed cost, assume 3 days)
    const guideCosts = mappingData.additional_costs.guide_cost_per_day * 3;

    console.log(`   🍽️ Meal costs: ₹${mappingData.additional_costs.meal_cost_per_person} × ${familyCount} = ₹${mealCosts}`);
    console.log(`   ⛴️ Ferry costs: ₹${mappingData.additional_costs.ferry_cost} × ${ferryEligiblePersons} (excluding ${familyType.no_of_infants} infants) = ₹${ferryCosts}`);
    console.log(`   🎯 Activity costs: ₹${mappingData.additional_costs.activity_cost_per_person} × ${familyCount} = ₹${activityCosts}`);
    console.log(`   👨‍🏫 Guide costs: ₹${mappingData.additional_costs.guide_cost_per_day} × 3 days = ₹${guideCosts}`);

    additionalCosts = mealCosts + ferryCosts + activityCosts + guideCosts;
  } else {
    console.log(`   ⚠️ No Quote Mapping data, using fallback calculation`);

    // Fallback: scale existing costs
    const scaledMeals = quoteData.costs.basicCosts.meals * peopleRatio;
    const scaledTrain = quoteData.costs.basicCosts.trainCost * peopleRatio;
    const scaledFerry = quoteData.costs.basicCosts.ferryCost;
    const scaledParking = quoteData.costs.basicCosts.parkingToll;
    additionalCosts = scaledMeals + scaledTrain + scaledFerry + scaledParking;

    console.log(`   🍽️ Scaled meals: ₹${scaledMeals}`);
    console.log(`   🚂 Scaled train: ₹${scaledTrain}`);
    console.log(`   ⛴️ Ferry: ₹${scaledFerry}`);
    console.log(`   🅿️ Parking: ₹${scaledParking}`);
  }

  console.log(`   ✅ Total Additional Costs: ₹${additionalCosts}`);

  return Math.round(additionalCosts);
};

// Main calculation function with proper logic
function calculatePriceForFamilyType(
  familyType: FamilyTypeDB,
  quoteData: QuoteGeneratorData, 
  mappingData: QuoteMappingData
): any {
  console.log(`\n💰 Calculating: ${familyType.family_type}`);
  console.log(`👥 Family: ${familyType.no_of_adults}A + ${familyType.no_of_children}C + ${familyType.no_of_infants}I + ${familyType.no_of_child}K = ${familyType.family_count} people`);
  
  // Calculate room requirements
  const roomReq = calculateRoomRequirements(familyType);
  console.log(`🏨 Rooms: ${roomReq.roomsNeeded}, Extra Adults: ${roomReq.extraAdults}`);
  
  // Calculate costs
  const hotelCost = calculateHotelCosts(familyType, roomReq, quoteData, mappingData);
  const vehicleCost = calculateVehicleCosts(familyType, quoteData, mappingData);
  const additionalCosts = calculateAdditionalCosts(familyType, quoteData, mappingData);
  
  console.log(`💰 Hotel: ₹${hotelCost}, Vehicle: ₹${vehicleCost}, Additional: ₹${additionalCosts}`);
  
  // Scale add-on and optional costs
  const baselinePeople = quoteData.noOfPersons + quoteData.extraAdults + 
                        quoteData.children + quoteData.infants;
  const peopleRatio = familyType.family_count / Math.max(baselinePeople, 1);
  
  const basicCosts = Math.round(Object.values(quoteData.costs.basicCosts).reduce((sum, cost) => sum + cost, 0) * peopleRatio);
  const addOnCosts = Math.round(Object.values(quoteData.costs.addOnCosts).reduce((sum, cost) => sum + cost, 0) * peopleRatio);
  const optionalCosts = Math.round(Object.values(quoteData.costs.optionalCosts).reduce((sum, cost) => sum + cost, 0) * peopleRatio);
  
  // Calculate subtotal
  const subtotal = hotelCost + vehicleCost + additionalCosts + addOnCosts + optionalCosts;
  
  // Apply discount
  let discount = 0;
  if (quoteData.discountType === 'percentage') {
    discount = subtotal * (quoteData.discountValue / 100);
  } else {
    discount = quoteData.discountValue;
  }
  
  const afterDiscount = Math.max(0, subtotal - discount);
  
  // Apply commission
  const commission = afterDiscount * (quoteData.commission / 100);
  const grandTotal = afterDiscount + commission;
  
  console.log(`💵 Subtotal: ₹${subtotal}, Discount: ₹${discount}, Commission: ₹${commission}, Total: ₹${grandTotal}`);
  
  return {
    hotelCost,
    vehicleCost,
    additionalCosts,
    basicCosts,
    addOnCosts,
    optionalCosts,
    subtotal: Math.round(subtotal),
    discount: Math.round(discount),
    commission: Math.round(commission),
    grandTotal: Math.round(grandTotal),
    rooms: roomReq.roomsNeeded,
    extraAdults: roomReq.extraAdults,
    childrenCharged: roomReq.childrenCharged,
    infantsFree: roomReq.infantsFree,
    notes: roomReq.notes
  };
}

export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
}; 